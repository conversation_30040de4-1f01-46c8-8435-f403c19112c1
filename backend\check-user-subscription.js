// 检查用户订阅和时间限制的脚本
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'database.sqlite');
const targetPhone = '13671910820';

console.log(`🔍 检查用户 ${targetPhone} 的订阅和时间限制...`);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 成功连接到SQLite数据库');
});

// 查询用户完整信息包括订阅
function getUserWithSubscription() {
  return new Promise((resolve, reject) => {
    db.get(`
      SELECT 
        u.*,
        s.id as subscription_id,
        s.plan as subscription_plan,
        s.startDate as subscription_start,
        s.endDate as subscription_end,
        s.status as subscription_status
      FROM users u
      LEFT JOIN subscriptions s ON u.id = s.userId
      WHERE u.phone = ?
    `, [targetPhone], (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

// 查询AppConfig配置
function getAppConfig() {
  return new Promise((resolve, reject) => {
    db.get(`SELECT * FROM app_configs LIMIT 1`, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

// 查询今日所有学习会话
function getTodayAllSessions() {
  return new Promise((resolve, reject) => {
    const today = new Date().toISOString().split('T')[0];
    db.all(`
      SELECT 
        id, userId, startTime, endTime, status,
        CASE 
          WHEN endTime IS NOT NULL THEN 
            (julianday(endTime) - julianday(startTime)) * 24 * 60 * 60
          ELSE 0
        END as duration_seconds
      FROM study_sessions
      WHERE userId = (SELECT id FROM users WHERE phone = ?)
      AND DATE(startTime) = ?
      ORDER BY startTime DESC
    `, [targetPhone, today], (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// 重置用户每日剩余时间
function resetDailyTime(userId, newRemainingSeconds) {
  return new Promise((resolve, reject) => {
    db.run(`
      UPDATE users 
      SET 
        dailyRemainingSeconds = ?,
        lastResetDate = datetime('now')
      WHERE id = ?
    `, [newRemainingSeconds, userId], function(err) {
      if (err) {
        reject(err);
      } else {
        console.log(`✅ 重置用户每日剩余时间为 ${newRemainingSeconds} 秒`);
        resolve(this.changes);
      }
    });
  });
}

// 删除今日学习会话
function deleteTodaySessions(userId) {
  return new Promise((resolve, reject) => {
    const today = new Date().toISOString().split('T')[0];
    db.run(`
      DELETE FROM study_sessions 
      WHERE userId = ? AND DATE(startTime) = ?
    `, [userId, today], function(err) {
      if (err) {
        reject(err);
      } else {
        console.log(`✅ 删除了 ${this.changes} 条今日学习会话`);
        resolve(this.changes);
      }
    });
  });
}

async function main() {
  try {
    // 1. 查看用户完整信息
    console.log('\n📊 用户完整信息:');
    const userInfo = await getUserWithSubscription();
    if (!userInfo) {
      console.log('❌ 未找到该用户');
      return;
    }
    
    console.log('用户ID:', userInfo.id);
    console.log('手机号:', userInfo.phone);
    console.log('试用结束日期:', userInfo.trialEndDate);
    console.log('订阅ID:', userInfo.subscription_id || 'NULL');
    console.log('订阅计划:', userInfo.subscription_plan || 'trial');
    console.log('订阅开始:', userInfo.subscription_start || 'NULL');
    console.log('订阅结束:', userInfo.subscription_end || 'NULL');
    console.log('订阅状态:', userInfo.subscription_status || 'inactive');
    console.log('累计Token使用:', userInfo.totalTokensUsed || 0);
    console.log('每日剩余秒数:', userInfo.dailyRemainingSeconds || 0);
    console.log('最后重置日期:', userInfo.lastResetDate || 'NULL');
    
    // 2. 查看AppConfig配置
    console.log('\n⚙️ 应用配置:');
    const appConfig = await getAppConfig();
    if (appConfig) {
      console.log('试用版每日限制:', appConfig.trialDailyLimitMinutes, '分钟');
      console.log('标准版每日限制:', appConfig.standardDailyLimitMinutes, '分钟');
      console.log('专业版每日限制:', appConfig.proDailyLimitMinutes, '分钟');
    }
    
    // 3. 计算应该有的时间限制
    let expectedDailyLimitMinutes = 60; // 默认试用版
    const now = new Date();
    const trialEndDate = new Date(userInfo.trialEndDate);
    const subscriptionEndDate = userInfo.subscription_end ? new Date(userInfo.subscription_end) : null;
    
    console.log('\n🕐 时间限制分析:');
    console.log('当前时间:', now.toISOString());
    console.log('试用结束时间:', trialEndDate.toISOString());
    console.log('订阅结束时间:', subscriptionEndDate ? subscriptionEndDate.toISOString() : 'NULL');
    
    if (subscriptionEndDate && subscriptionEndDate > now && userInfo.subscription_status === 'active') {
      // 有效订阅
      if (userInfo.subscription_plan === 'standard') {
        expectedDailyLimitMinutes = appConfig.standardDailyLimitMinutes;
      } else if (userInfo.subscription_plan === 'pro') {
        expectedDailyLimitMinutes = appConfig.proDailyLimitMinutes;
      }
      console.log('✅ 用户有有效订阅:', userInfo.subscription_plan);
    } else if (trialEndDate > now) {
      // 试用期内
      expectedDailyLimitMinutes = appConfig.trialDailyLimitMinutes;
      console.log('✅ 用户在试用期内');
    } else {
      // 无有效订阅和试用
      expectedDailyLimitMinutes = 0;
      console.log('❌ 用户无有效订阅或试用');
    }
    
    console.log('应有每日限制:', expectedDailyLimitMinutes, '分钟');
    console.log('当前剩余时间:', Math.floor(userInfo.dailyRemainingSeconds / 60), '分钟', userInfo.dailyRemainingSeconds % 60, '秒');
    
    // 4. 查看今日学习会话
    console.log('\n📅 今日学习会话:');
    const todaySessions = await getTodayAllSessions();
    if (todaySessions.length === 0) {
      console.log('今日无学习会话');
    } else {
      console.log(`今日共 ${todaySessions.length} 条学习会话:`);
      let totalUsedSeconds = 0;
      todaySessions.forEach((session, index) => {
        const duration = Math.round(session.duration_seconds || 0);
        totalUsedSeconds += duration;
        console.log(`  ${index + 1}. ${session.startTime} - ${session.endTime || 'NULL'} (${duration}秒)`);
      });
      console.log(`今日总使用时间: ${totalUsedSeconds}秒 (${Math.floor(totalUsedSeconds / 60)}分钟)`);
      
      const expectedRemainingSeconds = (expectedDailyLimitMinutes * 60) - totalUsedSeconds;
      console.log(`计算应剩余时间: ${expectedRemainingSeconds}秒 (${Math.floor(expectedRemainingSeconds / 60)}分钟)`);
      console.log(`实际剩余时间: ${userInfo.dailyRemainingSeconds}秒 (${Math.floor(userInfo.dailyRemainingSeconds / 60)}分钟)`);
      
      if (Math.abs(expectedRemainingSeconds - userInfo.dailyRemainingSeconds) > 60) {
        console.log('⚠️ 剩余时间不匹配，可能需要重置');
      }
    }
    
    // 5. 询问是否重置
    console.log('\n🔄 重置选项:');
    console.log('1. 重置每日剩余时间为完整限制');
    console.log('2. 删除今日学习会话并重置时间');
    console.log('3. 仅查看，不做修改');
    
    // 自动执行重置（选项2）
    console.log('\n执行选项2: 删除今日学习会话并重置时间...');
    
    await deleteTodaySessions(userInfo.id);
    await resetDailyTime(userInfo.id, expectedDailyLimitMinutes * 60);
    
    console.log('\n✅ 重置完成！');
    console.log(`用户现在应该有 ${expectedDailyLimitMinutes} 分钟的完整每日时间限制。`);
    
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('关闭数据库失败:', err.message);
      } else {
        console.log('\n🔌 数据库连接已关闭');
      }
    });
  }
}

main();
