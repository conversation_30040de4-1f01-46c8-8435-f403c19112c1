// 测试订阅时间调整功能的脚本
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'database.sqlite');
const targetPhone = '13671910820';

console.log('🧪 测试订阅时间调整功能...');

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 成功连接到SQLite数据库');
});

// 模拟不同的订阅变更场景
const testScenarios = [
  {
    name: '试用版→标准版（升级）',
    currentPlan: 'trial',
    currentLimit: 60,
    currentUsed: 30,
    newPlan: 'standard',
    newLimit: 180,
    expectedAdjustment: +120, // 增加120分钟
    expectedFinalRemaining: 150 // 30剩余 + 120增加 = 150
  },
  {
    name: '试用版→专业版（升级）',
    currentPlan: 'trial',
    currentLimit: 60,
    currentUsed: 20,
    newPlan: 'pro',
    newLimit: 300,
    expectedAdjustment: +240, // 增加240分钟
    expectedFinalRemaining: 280 // 40剩余 + 240增加 = 280
  },
  {
    name: '标准版→专业版（升级）',
    currentPlan: 'standard',
    currentLimit: 180,
    currentUsed: 60,
    newPlan: 'pro',
    newLimit: 300,
    expectedAdjustment: +120, // 增加120分钟
    expectedFinalRemaining: 240 // 120剩余 + 120增加 = 240
  },
  {
    name: '专业版→标准版（降级）',
    currentPlan: 'pro',
    currentLimit: 300,
    currentUsed: 100,
    newPlan: 'standard',
    newLimit: 180,
    expectedAdjustment: 0, // 降级不减时间
    expectedFinalRemaining: 200 // 保持200分钟不变
  },
  {
    name: '标准版→试用版（降级）',
    currentPlan: 'standard',
    currentLimit: 180,
    currentUsed: 50,
    newPlan: 'trial',
    newLimit: 60,
    expectedAdjustment: 0, // 降级不减时间
    expectedFinalRemaining: 130 // 保持130分钟不变
  }
];

function calculateTimeAdjustment(scenario) {
  const currentRemaining = scenario.currentLimit - scenario.currentUsed;
  const timeDifference = scenario.newLimit - scenario.currentLimit;
  
  let adjustment = 0;
  let finalRemaining = currentRemaining;
  
  if (timeDifference > 0) {
    // 升级：增加时间
    adjustment = timeDifference;
    finalRemaining = currentRemaining + adjustment;
  } else if (timeDifference < 0) {
    // 降级：保护用户，不减少时间
    adjustment = 0;
    finalRemaining = currentRemaining;
  }
  
  return { adjustment, finalRemaining };
}

function runTests() {
  console.log('\n📋 订阅时间调整测试用例:\n');
  
  testScenarios.forEach((scenario, index) => {
    console.log(`${index + 1}. ${scenario.name}`);
    console.log(`   当前: ${scenario.currentPlan} (${scenario.currentLimit}分钟)`);
    console.log(`   已用: ${scenario.currentUsed}分钟`);
    console.log(`   剩余: ${scenario.currentLimit - scenario.currentUsed}分钟`);
    console.log(`   升级到: ${scenario.newPlan} (${scenario.newLimit}分钟)`);
    
    const result = calculateTimeAdjustment(scenario);
    
    console.log(`   预期调整: ${scenario.expectedAdjustment > 0 ? '+' : ''}${scenario.expectedAdjustment}分钟`);
    console.log(`   实际调整: ${result.adjustment > 0 ? '+' : ''}${result.adjustment}分钟`);
    console.log(`   预期最终剩余: ${scenario.expectedFinalRemaining}分钟`);
    console.log(`   实际最终剩余: ${result.finalRemaining}分钟`);
    
    const adjustmentMatch = result.adjustment === scenario.expectedAdjustment;
    const finalMatch = result.finalRemaining === scenario.expectedFinalRemaining;
    const testPassed = adjustmentMatch && finalMatch;
    
    console.log(`   结果: ${testPassed ? '✅ 通过' : '❌ 失败'}`);
    
    if (!testPassed) {
      if (!adjustmentMatch) {
        console.log(`   ❌ 调整时间不匹配: 期望${scenario.expectedAdjustment}, 实际${result.adjustment}`);
      }
      if (!finalMatch) {
        console.log(`   ❌ 最终剩余时间不匹配: 期望${scenario.expectedFinalRemaining}, 实际${result.finalRemaining}`);
      }
    }
    
    console.log('');
  });
  
  console.log('🎯 测试总结:');
  console.log('✅ 升级时增加时间差');
  console.log('✅ 降级时保护用户，不减少当天剩余时间');
  console.log('✅ 用户永远不会因为订阅变更而损失时间');
  
  console.log('\n📝 业务规则:');
  console.log('1. 升级订阅：用户获得额外时间 = 新限制 - 旧限制');
  console.log('2. 降级订阅：用户当天剩余时间保持不变');
  console.log('3. 用户体验：升级有奖励，降级有保护');
  
  console.log('\n🔧 实现要点:');
  console.log('- 在订阅成功回调中调用时间调整服务');
  console.log('- 提供预览API让用户了解时间变化');
  console.log('- 记录调整日志便于客服处理问题');
}

// 运行测试
runTests();

// 关闭数据库连接
db.close((err) => {
  if (err) {
    console.error('关闭数据库失败:', err.message);
  } else {
    console.log('\n🔌 数据库连接已关闭');
  }
});
