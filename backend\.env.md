# Backend Environment Variables

This file documents the environment variables required by the backend server. Create a `.env` file in the `backend` directory and add the following variables.

## Database Configuration
- `DATABASE_PATH`: SQLite database file path for local development (default: `./database.sqlite`)
- `CLOUD_NATIVE_DB_PATH`: SQLite database file path for cloud native deployment (e.g., `/app/data/database.sqlite`)

## JWT
- `JWT_SECRET`: A secret key for signing JSON Web Tokens.
- `JWT_EXPIRES_IN`: Token expiration time (default: `7d`)

## Qwen AI
- `QWEN_API_KEY`: Your API key for the Qwen AI service.
- `QWEN_API_BASE_URL`: The base URL for the Qwen AI service.

## Alipay
- `ALIPAY_APP_ID`: Your application ID for <PERSON>pay.
- `ALIPAY_PRIVATE_KEY`: Your private key for Alipay.
- `ALIPAY_PUBLIC_KEY`: Your public key for Alipay.
- `ALIPAY_GATEWAY_URL`: The gateway URL for the Alipay service.
- `USE_MOCK_PAYMENT`: Set to `true` to use mock payment for development.

## Upload Configuration
- `UPLOAD_STRATEGY`: Upload strategy (`local` for local storage, `oss` for Alibaba Cloud OSS)
- `BACKEND_URL`: Backend server URL for local file access
- `OSS_ACCESS_KEY_ID`: OSS access key ID (when using OSS)
- `OSS_ACCESS_KEY_SECRET`: OSS access key secret (when using OSS)
- `OSS_REGION`: OSS region (when using OSS)
- `OSS_BUCKET`: OSS bucket name (when using OSS)
- `OSS_HOST`: OSS host URL (when using OSS)
- `OSS_EXPIRE_TIME`: OSS signature expiration time in seconds (when using OSS)

## Admin
- `ADMIN_PHONE`: The phone number of the administrator account. This user will have access to the admin configuration panel.
- `ADMIN_KEY`: Admin authentication key.

## Server Configuration
- `PORT`: Server port (default: 5000)
- `FRONTEND_URL`: Frontend application URL
- `NODE_ENV`: Environment mode (`development` or `production`)

## Gamification
- `STONE_MONKEY_GOAL_TOKENS`: Token goal for Stone Monkey stage
- `CAVE_MASTER_GOAL_TOKENS`: Token goal for Cave Master stage
- `MONKEY_KING_GOAL_TOKENS`: Token goal for Monkey King stage
