
import { Router } from 'express';
import { createSubscriptionOrder, handleAlipayWebhook, getSubscriptionTimePreview } from '../controllers/subscription.controller';
import { protect } from '../middleware/auth.middleware';

const router = Router();

router.post('/create-order', protect, createSubscriptionOrder);
router.get('/time-preview', protect, getSubscriptionTimePreview); // 获取订阅变更时间预览
router.post('/webhook/alipay', handleAlipayWebhook); // This webhook is open

export default router;
