// 检查数据库表结构的脚本
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'database.sqlite');
const targetPhone = '13671910820';

console.log('🔍 检查数据库表结构...');

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 成功连接到SQLite数据库');
});

// 查询用户表结构
function getUserTableStructure() {
  return new Promise((resolve, reject) => {
    db.all("PRAGMA table_info(users)", (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// 查询学习会话表结构
function getStudySessionsTableStructure() {
  return new Promise((resolve, reject) => {
    db.all("PRAGMA table_info(study_sessions)", (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// 查询用户详细信息
function getUserDetails() {
  return new Promise((resolve, reject) => {
    db.get(`SELECT * FROM users WHERE phone = ?`, [targetPhone], (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

// 查询今日学习会话详情
function getTodaySessionsDetails() {
  return new Promise((resolve, reject) => {
    const today = new Date().toISOString().split('T')[0];
    db.all(`
      SELECT *
      FROM study_sessions
      WHERE userId = (SELECT id FROM users WHERE phone = ?)
      AND DATE(startTime) = ?
      ORDER BY startTime DESC
    `, [targetPhone, today], (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

async function main() {
  try {
    // 1. 检查用户表结构
    console.log('\n📋 用户表 (users) 结构:');
    const userTableStructure = await getUserTableStructure();
    userTableStructure.forEach(column => {
      console.log(`  ${column.name}: ${column.type} ${column.notnull ? 'NOT NULL' : ''} ${column.dflt_value ? `DEFAULT ${column.dflt_value}` : ''}`);
    });
    
    // 2. 检查学习会话表结构
    console.log('\n📋 学习会话表 (study_sessions) 结构:');
    const studySessionsStructure = await getStudySessionsTableStructure();
    studySessionsStructure.forEach(column => {
      console.log(`  ${column.name}: ${column.type} ${column.notnull ? 'NOT NULL' : ''} ${column.dflt_value ? `DEFAULT ${column.dflt_value}` : ''}`);
    });
    
    // 3. 查看用户详细信息
    console.log(`\n📊 用户 ${targetPhone} 详细信息:`);
    const userDetails = await getUserDetails();
    if (userDetails) {
      Object.entries(userDetails).forEach(([key, value]) => {
        console.log(`  ${key}: ${value}`);
      });
    } else {
      console.log('❌ 未找到该用户');
    }
    
    // 4. 查看今日学习会话详情
    console.log('\n📅 今日学习会话详情:');
    const todaySessions = await getTodaySessionsDetails();
    if (todaySessions.length === 0) {
      console.log('今日无学习会话');
    } else {
      console.log(`今日共 ${todaySessions.length} 条学习会话:`);
      todaySessions.forEach((session, index) => {
        console.log(`\n  会话 ${index + 1}:`);
        Object.entries(session).forEach(([key, value]) => {
          console.log(`    ${key}: ${value}`);
        });
      });
    }
    
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('关闭数据库失败:', err.message);
      } else {
        console.log('\n🔌 数据库连接已关闭');
      }
    });
  }
}

main();
