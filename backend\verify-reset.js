// 验证重置结果的脚本
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'database.sqlite');
const targetPhone = '13671910820';

console.log(`🔍 验证用户 ${targetPhone} 的重置结果...`);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 成功连接到SQLite数据库');
});

// 查询用户当前状态
function getCurrentUserStatus() {
  return new Promise((resolve, reject) => {
    db.get(`
      SELECT 
        u.id,
        u.phone,
        u.dailyRemainingSeconds,
        u.lastResetDate,
        u.totalTokensUsed,
        s.plan as subscription_plan,
        s.status as subscription_status
      FROM users u
      LEFT JOIN subscriptions s ON u.id = s.userId
      WHERE u.phone = ?
    `, [targetPhone], (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

async function main() {
  try {
    const userStatus = await getCurrentUserStatus();
    
    if (!userStatus) {
      console.log('❌ 未找到该用户');
      return;
    }
    
    console.log('\n📊 重置后用户状态:');
    console.log('用户ID:', userStatus.id);
    console.log('手机号:', userStatus.phone);
    console.log('订阅计划:', userStatus.subscription_plan);
    console.log('订阅状态:', userStatus.subscription_status);
    console.log('每日剩余秒数:', userStatus.dailyRemainingSeconds);
    console.log('每日剩余时间:', Math.floor(userStatus.dailyRemainingSeconds / 60), '分钟', userStatus.dailyRemainingSeconds % 60, '秒');
    console.log('最后重置日期:', userStatus.lastResetDate);
    console.log('累计Token使用:', userStatus.totalTokensUsed);
    
    // 验证结果
    const expectedSeconds = 180 * 60; // 标准版180分钟
    console.log('\n✅ 验证结果:');
    console.log('期望剩余时间:', expectedSeconds, '秒 (180分钟)');
    console.log('实际剩余时间:', userStatus.dailyRemainingSeconds, '秒');
    
    if (userStatus.dailyRemainingSeconds === expectedSeconds) {
      console.log('🎉 重置成功！用户现在有完整的180分钟每日时间限制。');
      console.log('前端界面应该显示: 180:00 / 180:00');
    } else {
      console.log('⚠️ 重置可能有问题，剩余时间不匹配期望值。');
    }
    
    // 显示时间格式
    const hours = Math.floor(userStatus.dailyRemainingSeconds / 3600);
    const minutes = Math.floor((userStatus.dailyRemainingSeconds % 3600) / 60);
    const seconds = userStatus.dailyRemainingSeconds % 60;
    
    console.log('\n🕐 前端显示格式:');
    console.log(`剩余时间: ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
    console.log(`总时间: 03:00:00 (180分钟)`);
    console.log(`应显示: ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')} / 180:00`);
    
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('关闭数据库失败:', err.message);
      } else {
        console.log('\n🔌 数据库连接已关闭');
      }
    });
  }
}

main();
