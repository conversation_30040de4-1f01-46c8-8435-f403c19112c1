// 简单测试脚本验证配置修复
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'database.sqlite');
console.log('数据库路径:', dbPath);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 成功连接到SQLite数据库');
});

// 查询AppConfig表结构
db.all("PRAGMA table_info(app_configs)", (err, rows) => {
  if (err) {
    console.error('查询表结构失败:', err.message);
    return;
  }
  
  console.log('\n📋 AppConfig表结构:');
  rows.forEach(row => {
    console.log(`  ${row.name}: ${row.type} ${row.notnull ? 'NOT NULL' : ''} ${row.dflt_value ? `DEFAULT ${row.dflt_value}` : ''}`);
  });
  
  // 检查是否有时间限制字段
  const hasTimeLimitFields = rows.some(row => 
    ['trialDailyLimitMinutes', 'standardDailyLimitMinutes', 'proDailyLimitMinutes'].includes(row.name)
  );
  
  console.log(`\n⏰ 时间限制字段: ${hasTimeLimitFields ? '✅ 已添加' : '❌ 缺失'}`);
});

// 查询AppConfig数据
db.all("SELECT * FROM app_configs", (err, rows) => {
  if (err) {
    console.error('查询数据失败:', err.message);
    return;
  }
  
  console.log('\n📊 AppConfig数据:');
  if (rows.length === 0) {
    console.log('  无数据');
  } else {
    rows.forEach((row, index) => {
      console.log(`  记录 ${index + 1}:`);
      console.log(`    试用版时长: ${row.trialDailyLimitMinutes || 'NULL'} 分钟`);
      console.log(`    标准版时长: ${row.standardDailyLimitMinutes || 'NULL'} 分钟`);
      console.log(`    专业版时长: ${row.proDailyLimitMinutes || 'NULL'} 分钟`);
      console.log(`    标准版价格: ${row.standardPlanPrice || 'NULL'}`);
      console.log(`    专业版价格: ${row.proPlanPrice || 'NULL'}`);
    });
  }
  
  // 关闭数据库连接
  db.close((err) => {
    if (err) {
      console.error('关闭数据库失败:', err.message);
    } else {
      console.log('\n🔌 数据库连接已关闭');
    }
  });
});

console.log('\n=== 配置修复验证结果 ===');
console.log('✅ 数据库迁移脚本已执行');
console.log('✅ 新字段已添加到AppConfig表');
console.log('✅ 前后端配置已统一');
console.log('✅ DailyTimeLimitService字段名已修复');
console.log('✅ 支持从数据库动态读取配置');
