// 重置用户数据的脚本
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'database.sqlite');
const targetPhone = '13671910820';

console.log(`🔍 检查用户 ${targetPhone} 的数据...`);

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 成功连接到SQLite数据库');
});

// 查询用户基本信息
function getUserInfo() {
  return new Promise((resolve, reject) => {
    db.get(`
      SELECT u.*, s.plan, s.startDate, s.endDate, s.status as subscriptionStatus
      FROM users u
      LEFT JOIN subscriptions s ON u.id = s.userId
      WHERE u.phone = ?
    `, [targetPhone], (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

// 查询用户今日学习会话
function getTodayStudySessions() {
  return new Promise((resolve, reject) => {
    const today = new Date().toISOString().split('T')[0];
    db.all(`
      SELECT *
      FROM study_sessions
      WHERE userId = (SELECT id FROM users WHERE phone = ?)
      AND DATE(startTime) = ?
      ORDER BY startTime DESC
    `, [targetPhone, today], (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// 查询用户所有学习会话（最近10条）
function getRecentStudySessions() {
  return new Promise((resolve, reject) => {
    db.all(`
      SELECT *
      FROM study_sessions
      WHERE userId = (SELECT id FROM users WHERE phone = ?)
      ORDER BY startTime DESC
      LIMIT 10
    `, [targetPhone], (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// 重置用户数据
function resetUserData() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      db.run('BEGIN TRANSACTION');
      
      // 删除用户的学习会话
      db.run(`
        DELETE FROM study_sessions 
        WHERE userId = (SELECT id FROM users WHERE phone = ?)
      `, [targetPhone], function(err) {
        if (err) {
          console.error('删除学习会话失败:', err);
          db.run('ROLLBACK');
          reject(err);
          return;
        }
        console.log(`✅ 删除了 ${this.changes} 条学习会话记录`);
      });
      
      // 重置用户的累计数据
      db.run(`
        UPDATE users 
        SET 
          totalFocusSeconds = 0,
          totalSpiritualPower = 0,
          gamificationStage = 'STONE_MONKEY',
          lastActiveDate = NULL
        WHERE phone = ?
      `, [targetPhone], function(err) {
        if (err) {
          console.error('重置用户数据失败:', err);
          db.run('ROLLBACK');
          reject(err);
          return;
        }
        console.log(`✅ 重置了用户累计数据`);
      });
      
      db.run('COMMIT', (err) => {
        if (err) {
          console.error('提交事务失败:', err);
          reject(err);
        } else {
          console.log('✅ 数据重置完成');
          resolve();
        }
      });
    });
  });
}

async function main() {
  try {
    // 1. 查看用户当前信息
    console.log('\n📊 用户当前信息:');
    const userInfo = await getUserInfo();
    if (!userInfo) {
      console.log('❌ 未找到该用户');
      return;
    }
    
    console.log('用户ID:', userInfo.id);
    console.log('手机号:', userInfo.phone);
    console.log('订阅计划:', userInfo.plan || 'trial');
    console.log('订阅状态:', userInfo.subscriptionStatus || 'inactive');
    console.log('累计专注时间:', userInfo.totalFocusSeconds || 0, '秒');
    console.log('累计灵力:', userInfo.totalSpiritualPower || 0);
    console.log('游戏化阶段:', userInfo.gamificationStage || 'STONE_MONKEY');
    console.log('最后活跃日期:', userInfo.lastActiveDate || 'NULL');
    
    // 2. 查看今日学习会话
    console.log('\n📅 今日学习会话:');
    const todaySessions = await getTodayStudySessions();
    if (todaySessions.length === 0) {
      console.log('今日无学习会话');
    } else {
      console.log(`今日共 ${todaySessions.length} 条学习会话:`);
      todaySessions.forEach((session, index) => {
        console.log(`  ${index + 1}. 开始: ${session.startTime}, 结束: ${session.endTime || 'NULL'}, 时长: ${session.focusSeconds || 0}秒`);
      });
      
      const todayTotalSeconds = todaySessions.reduce((sum, session) => sum + (session.focusSeconds || 0), 0);
      console.log(`今日总专注时间: ${todayTotalSeconds}秒 (${Math.floor(todayTotalSeconds / 60)}分钟)`);
    }
    
    // 3. 查看最近学习会话
    console.log('\n📈 最近学习会话 (最多10条):');
    const recentSessions = await getRecentStudySessions();
    if (recentSessions.length === 0) {
      console.log('无学习会话记录');
    } else {
      console.log(`共 ${recentSessions.length} 条记录:`);
      recentSessions.forEach((session, index) => {
        const date = new Date(session.startTime).toLocaleDateString();
        console.log(`  ${index + 1}. ${date} - 时长: ${session.focusSeconds || 0}秒`);
      });
    }
    
    // 4. 询问是否重置
    console.log('\n⚠️  准备重置用户数据...');
    console.log('将执行以下操作:');
    console.log('- 删除所有学习会话记录');
    console.log('- 重置累计专注时间为0');
    console.log('- 重置累计灵力为0');
    console.log('- 重置游戏化阶段为STONE_MONKEY');
    console.log('- 清除最后活跃日期');
    
    // 直接执行重置（在脚本中自动确认）
    console.log('\n🔄 开始重置数据...');
    await resetUserData();
    
    // 5. 验证重置结果
    console.log('\n✅ 重置完成，验证结果:');
    const resetUserInfo = await getUserInfo();
    console.log('累计专注时间:', resetUserInfo.totalFocusSeconds || 0, '秒');
    console.log('累计灵力:', resetUserInfo.totalSpiritualPower || 0);
    console.log('游戏化阶段:', resetUserInfo.gamificationStage || 'STONE_MONKEY');
    console.log('最后活跃日期:', resetUserInfo.lastActiveDate || 'NULL');
    
    const resetTodaySessions = await getTodayStudySessions();
    console.log('今日学习会话数量:', resetTodaySessions.length);
    
    console.log('\n🎉 用户数据重置完成！');
    console.log('现在用户应该有完整的每日时间限制可用。');
    
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('关闭数据库失败:', err.message);
      } else {
        console.log('🔌 数据库连接已关闭');
      }
    });
  }
}

main();
