// 测试API配置的脚本
const http = require('http');

function testAPI(path, description) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 5000,
            path: path,
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`\n=== ${description} ===`);
                console.log(`Status: ${res.statusCode}`);
                console.log(`Headers:`, res.headers);
                
                try {
                    const jsonData = JSON.parse(data);
                    console.log('Response:', JSON.stringify(jsonData, null, 2));
                } catch (e) {
                    console.log('Raw Response:', data.substring(0, 200) + (data.length > 200 ? '...' : ''));
                }
                
                resolve({ status: res.statusCode, data });
            });
        });

        req.on('error', (err) => {
            console.log(`\n=== ${description} - ERROR ===`);
            console.log('Error:', err.message);
            reject(err);
        });

        req.end();
    });
}

async function runTests() {
    console.log('🧪 开始API测试...\n');
    
    try {
        // 测试健康检查
        await testAPI('/health', '健康检查');
        
        // 测试计划详情
        await testAPI('/api/config/plan-details', '计划详情配置');
        
        // 测试游戏配置
        await testAPI('/api/config/game', '游戏化目标配置');
        
        // 测试应用配置
        await testAPI('/api/config', '应用配置');
        
        console.log('\n✅ 所有API测试完成');
        
    } catch (error) {
        console.log('\n❌ API测试失败:', error.message);
    }
}

runTests();
