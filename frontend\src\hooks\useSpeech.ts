
import { useCallback } from 'react';
import { playAudio, stopCurrentAudio } from '../utils/audioUtils';

// Minimal type declarations to fix compiler errors when DOM library is not present.
declare class SpeechSynthesisUtterance extends EventTarget {
    constructor(text?: string);
    text: string;
    lang: string;
    rate: number;
    pitch: number;
    onend: ((this: SpeechSynthesisUtterance, ev: Event) => any) | null;
}
interface SpeechSynthesis {
    cancel(): void;
    speak(utterance: SpeechSynthesisUtterance): void;
    speaking: boolean;
}

export const useSpeech = () => {
  // 使用TTS的原始speak函数
  const speakWithTTS = useCallback((text: string, lang = 'zh-CN', onEnd?: () => void) => {
    if (typeof window !== 'undefined' && 'speechSynthesis' in window && text) {
      const synth = window.speechSynthesis as SpeechSynthesis;
      // Cancel any ongoing speech to prevent overlap
      synth.cancel();
      
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = lang;
      utterance.rate = 1.0;
      utterance.pitch = 1.0;
      if (onEnd) {
          utterance.onend = onEnd as any;
      }
      synth.speak(utterance);
    } else {
      console.warn('Speech synthesis not supported in this browser or text is empty.');
      if (onEnd) {
          onEnd();
      }
    }
  }, []);

  // 增强的speak函数，优先使用音频文件，降级到TTS
  const speak = useCallback((text: string, audioUrl?: string, lang = 'zh-CN', onEnd?: () => void) => {
    // 如果提供了音频URL，优先使用音频播放
    if (audioUrl) {
      console.log(`使用音频文件播放: ${audioUrl}`);
      playAudio(audioUrl, onEnd, (error) => {
        console.warn('音频播放失败，降级到TTS:', error.message);
        // 音频播放失败时降级到TTS
        speakWithTTS(text, lang, onEnd);
      }).catch(() => {
        // 如果playAudio抛出异常，也降级到TTS
        console.warn('音频播放异常，降级到TTS');
        speakWithTTS(text, lang, onEnd);
      });
    } else {
      // 没有音频URL时直接使用TTS
      console.log('使用TTS播放文本:', text);
      speakWithTTS(text, lang, onEnd);
    }
  }, [speakWithTTS]);

  // 停止当前播放
  const stopSpeaking = useCallback(() => {
    // 停止音频播放
    stopCurrentAudio();
    
    // 停止TTS
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      const synth = window.speechSynthesis as SpeechSynthesis;
      synth.cancel();
    }
  }, []);

  return { speak, stopSpeaking };
};
