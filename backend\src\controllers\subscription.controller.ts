import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest, SubscriptionPlan } from '../types';
import { generateAlipayUrl, verifyAlipaySign } from '../services/alipay.service';
import { User, Subscription } from '../models';
import { SubscriptionTimeAdjustmentService } from '../services/subscriptionTimeAdjustment.service';

export const createSubscriptionOrder = async (req: Request, res: Response, next: NextFunction) => {
    const authReq = req as AuthenticatedRequest;
    const { plan } = (req as any).body as { plan: string };
    const userId = authReq.user!.id;

    if (!plan || !['standard', 'pro'].includes(plan)) {
        return (res as any).status(400).json({ message: '无效的订阅计划' });
    }

    try {
        const { qrCode, outTradeNo } = await generateAlipayUrl(userId, plan as Exclude<SubscriptionPlan, 'trial' | 'none'>);

        // Here you could create a pending subscription record in your DB
        // with the outTradeNo for later verification.

        (res as any).json({ qrCode, orderId: outTradeNo });
    } catch (error) {
        next(error);
    }
};

export const handleAlipayWebhook = async (req: Request, res: Response, next: NextFunction) => {
    console.log('Received Alipay webhook:', (req as any).body);
    const params = (req as any).body;
    
    // For a real implementation, add robust validation here.
    // This is a simplified mock.
    const isVerified = verifyAlipaySign(params);

    if (isVerified && params.trade_status === 'TRADE_SUCCESS') {
        const outTradeNo = params.out_trade_no; // Your internal order ID
        const alipayTradeNo = params.trade_no; // Alipay's transaction ID
        
        // The out_trade_no should contain info to identify the user and plan.
        // e.g., 'sub-standard-USERID-TIMESTAMP'
        const [type, plan, userId] = outTradeNo.split('-');

        if (type !== 'sub' || !userId) {
            return (res as any).status(400).send('failure_invalid_order');
        }

        try {
            const user = await User.findByPk(userId);
            if (!user) return (res as any).status(404).send('failure_user_not_found');

            const now = new Date();
            const endDate = new Date(now.setMonth(now.getMonth() + 1));

            // 在创建订阅前，先调整用户的每日时间限制
            await SubscriptionTimeAdjustmentService.adjustTimeOnSubscriptionChange(
                userId,
                plan as 'standard' | 'pro'
            );

            // Find existing subscription and update it, or create a new one
            const [subscription, created] = await Subscription.upsert({
                userId,
                plan,
                status: 'active',
                startDate: new Date(),
                endDate,
                alipayTradeNo,
            });

            console.log(`Successfully activated subscription for user ${userId} with time adjustment`);
            (res as any).status(200).send('success');
        } catch (error) {
            console.error('Webhook processing error:', error);
            (res as any).status(500).send('failure_server_error');
        }

    } else {
        console.log('Alipay webhook verification failed or trade not successful.');
        (res as any).status(400).send('failure');
    }
};

/**
 * 获取订阅变更的时间调整预览
 */
export const getSubscriptionTimePreview = async (req: Request, res: Response, next: NextFunction) => {
    const authReq = req as AuthenticatedRequest;
    const { plan } = req.query as { plan: string };
    const userId = authReq.user!.id;

    if (!plan || !['trial', 'standard', 'pro'].includes(plan)) {
        return (res as any).status(400).json({ message: '无效的订阅计划' });
    }

    try {
        const preview = await SubscriptionTimeAdjustmentService.getTimeAdjustmentPreview(
            userId,
            plan as 'trial' | 'standard' | 'pro'
        );

        (res as any).json({
            success: true,
            data: {
                currentPlan: preview.currentPlan,
                targetPlan: preview.targetPlan,
                currentRemainingMinutes: Math.floor(preview.currentRemaining / 60),
                newRemainingMinutes: Math.floor(preview.newRemaining / 60),
                adjustmentMinutes: Math.floor(preview.adjustment / 60),
                adjustmentType: preview.adjustmentType,
                message: preview.adjustmentType === 'upgrade'
                    ? `升级后将获得额外 ${Math.floor(preview.adjustment / 60)} 分钟`
                    : preview.adjustmentType === 'downgrade'
                    ? '降级后当天剩余时间保持不变'
                    : '计划无变化'
            }
        });
    } catch (error) {
        next(error);
    }
};