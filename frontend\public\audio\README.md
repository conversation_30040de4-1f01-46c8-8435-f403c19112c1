# 音频文件配置说明

## 目录结构
本目录用于存放反馈消息对应的音频文件。

## 文件命名规范

### 专业版 (Pro) 音频文件
- `pro_focused_1.mp3` - `pro_focused_4.mp3` (专注状态)
- `pro_off_seat_1.mp3` - `pro_off_seat_4.mp3` (离座状态)
- `pro_play_1.mp3` - `pro_play_4.mp3` (玩耍状态)
- `pro_distracted_1.mp3` - `pro_distracted_4.mp3` (分心状态)
- `pro_zone_1.mp3` - `pro_zone_4.mp3` (发呆状态)
- `pro_talk_1.mp3` - `pro_talk_4.mp3` (说话状态)
- `pro_sleep_1.mp3` - `pro_sleep_4.mp3` (睡觉状态)

### 标准版 (Standard) 音频文件
- `standard_focused_1.mp3` - `standard_focused_4.mp3` (专注状态)
- `standard_off_seat_1.mp3` - `standard_off_seat_4.mp3` (离座状态)
- `standard_play_1.mp3` - `standard_play_4.mp3` (玩耍状态)
- `standard_distracted_1.mp3` - `standard_distracted_4.mp3` (分心状态)
- `standard_zone_1.mp3` - `standard_zone_4.mp3` (发呆状态)
- `standard_talk_1.mp3` - `standard_talk_4.mp3` (说话状态)
- `standard_sleep_1.mp3` - `standard_sleep_4.mp3` (睡觉状态)

## 音频文件要求
- 格式：MP3、WAV、OGG 等浏览器支持的音频格式
- 时长：建议 3-8 秒
- 音质：清晰，无杂音
- 音量：适中，避免过大或过小

## 部署说明

### 本地开发环境
- 将音频文件直接放置在此目录下
- 系统会自动从 `/audio/` 路径加载音频文件

### 生产环境 (云OSS)
- 将音频文件上传到云OSS存储
- 在 `.env.production` 中配置 `VITE_OSS_AUDIO_BASE_URL`
- 系统会自动从OSS加载音频文件

## 配置更新
音频文件上传后，需要在管理后台更新对应反馈消息的 `audioUrl` 字段，格式为文件名（如：`pro_focused_1.mp3`）。

## 总计
- 专业版：7个状态 × 4条消息 = 28个音频文件
- 标准版：7个状态 × 4条消息 = 28个音频文件
- **总计：56个音频文件**