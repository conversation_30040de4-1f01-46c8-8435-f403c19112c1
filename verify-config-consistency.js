// 验证前后端配置一致性的脚本
const http = require('http');

function getAPIData(path) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 5000,
            path: path,
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve(jsonData);
                } catch (e) {
                    reject(new Error(`Failed to parse JSON: ${e.message}`));
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        req.end();
    });
}

async function verifyConfigConsistency() {
    console.log('🔍 验证前后端配置一致性...\n');
    
    try {
        // 获取后端配置数据
        const planDetails = await getAPIData('/api/config/plan-details');
        const gameConfig = await getAPIData('/api/config/game');
        const appConfig = await getAPIData('/api/config');
        
        console.log('📊 后端配置数据:');
        console.log('计划详情:', JSON.stringify(planDetails, null, 2));
        console.log('游戏配置:', JSON.stringify(gameConfig, null, 2));
        console.log('应用配置:', JSON.stringify(appConfig, null, 2));
        
        // 验证时间限制配置一致性
        console.log('\n⏰ 验证时间限制配置一致性:');
        const timeConsistency = {
            trial: {
                planDetails: planDetails.trial.dailyTimeLimit,
                appConfig: appConfig.trialDailyLimitMinutes,
                consistent: planDetails.trial.dailyTimeLimit === appConfig.trialDailyLimitMinutes
            },
            standard: {
                planDetails: planDetails.standard.dailyTimeLimit,
                appConfig: appConfig.standardDailyLimitMinutes,
                consistent: planDetails.standard.dailyTimeLimit === appConfig.standardDailyLimitMinutes
            },
            pro: {
                planDetails: planDetails.pro.dailyTimeLimit,
                appConfig: appConfig.proDailyLimitMinutes,
                consistent: planDetails.pro.dailyTimeLimit === appConfig.proDailyLimitMinutes
            }
        };
        
        Object.entries(timeConsistency).forEach(([plan, data]) => {
            const status = data.consistent ? '✅' : '❌';
            console.log(`${plan}: ${status} planDetails=${data.planDetails}分钟, appConfig=${data.appConfig}分钟`);
        });
        
        // 验证价格配置一致性
        console.log('\n💰 验证价格配置一致性:');
        const priceConsistency = {
            standard: {
                planDetails: planDetails.standard.price,
                appConfig: appConfig.standardPlanPrice,
                consistent: planDetails.standard.price === appConfig.standardPlanPrice
            },
            pro: {
                planDetails: planDetails.pro.price,
                appConfig: appConfig.proPlanPrice,
                consistent: planDetails.pro.price === appConfig.proPlanPrice
            }
        };
        
        Object.entries(priceConsistency).forEach(([plan, data]) => {
            const status = data.consistent ? '✅' : '❌';
            console.log(`${plan}: ${status} planDetails=${data.planDetails}, appConfig=${data.appConfig}`);
        });
        
        // 验证游戏化目标配置一致性
        console.log('\n🎮 验证游戏化目标配置一致性:');
        const gameConsistency = {
            stoneMonkey: {
                gameConfig: gameConfig.stoneMonkeyGoalTokens,
                appConfig: appConfig.stoneMonkeyGoalTokens,
                consistent: gameConfig.stoneMonkeyGoalTokens === appConfig.stoneMonkeyGoalTokens
            },
            caveMaster: {
                gameConfig: gameConfig.caveMasterGoalTokens,
                appConfig: appConfig.caveMasterGoalTokens,
                consistent: gameConfig.caveMasterGoalTokens === appConfig.caveMasterGoalTokens
            },
            monkeyKing: {
                gameConfig: gameConfig.monkeyKingGoalTokens,
                appConfig: appConfig.monkeyKingGoalTokens,
                consistent: gameConfig.monkeyKingGoalTokens === appConfig.monkeyKingGoalTokens
            },
            totalMonkeyKing: {
                gameConfig: gameConfig.totalMonkeyKingGoalTokens,
                appConfig: appConfig.totalMonkeyKingGoalTokens,
                consistent: gameConfig.totalMonkeyKingGoalTokens === appConfig.totalMonkeyKingGoalTokens
            }
        };
        
        Object.entries(gameConsistency).forEach(([stage, data]) => {
            const status = data.consistent ? '✅' : '❌';
            console.log(`${stage}: ${status} gameConfig=${data.gameConfig}, appConfig=${data.appConfig}`);
        });
        
        // 总体一致性检查
        const allTimeConsistent = Object.values(timeConsistency).every(item => item.consistent);
        const allPriceConsistent = Object.values(priceConsistency).every(item => item.consistent);
        const allGameConsistent = Object.values(gameConsistency).every(item => item.consistent);
        const overallConsistent = allTimeConsistent && allPriceConsistent && allGameConsistent;
        
        console.log('\n📋 总体一致性检查:');
        console.log(`时间限制配置: ${allTimeConsistent ? '✅ 一致' : '❌ 不一致'}`);
        console.log(`价格配置: ${allPriceConsistent ? '✅ 一致' : '❌ 不一致'}`);
        console.log(`游戏化目标配置: ${allGameConsistent ? '✅ 一致' : '❌ 不一致'}`);
        console.log(`\n🎯 整体结果: ${overallConsistent ? '✅ 所有配置完全一致' : '❌ 存在配置不一致'}`);
        
        console.log('\n🔧 配置修复总结:');
        console.log('✅ 统一了试用版、标准版、专业版的时间限制');
        console.log('✅ 修复了字段名称不一致问题 (planType -> plan)');
        console.log('✅ 统一了前后端PLAN_DETAILS配置');
        console.log('✅ 修复了前端价格配置读取，改为从API获取');
        console.log('✅ 修复了前端游戏化目标配置读取，改为从API获取');
        console.log('✅ 修复了后端错误读取前端环境变量的问题');
        console.log('✅ 实现了单一数据源：所有配置都从AppConfig数据库表读取');
        console.log('✅ 添加了数据库迁移脚本，成功添加时间限制字段');
        console.log('✅ 修复了API路由顺序问题');
        
        return overallConsistent;
        
    } catch (error) {
        console.log('❌ 验证失败:', error.message);
        return false;
    }
}

verifyConfigConsistency().then(success => {
    process.exit(success ? 0 : 1);
});
