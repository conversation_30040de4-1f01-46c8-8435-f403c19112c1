import { User, Subscription } from '../models';
import { getPlanDetails } from '../types';

/**
 * 订阅变更时的时间调整服务
 * 原则：升级时加时间，降级时保护用户不减时间
 */
export class SubscriptionTimeAdjustmentService {
  
  /**
   * 处理订阅变更时的时间调整
   * @param userId 用户ID
   * @param newPlan 新的订阅计划
   * @param oldPlan 旧的订阅计划（可选，如果不提供会自动获取）
   */
  static async adjustTimeOnSubscriptionChange(
    userId: string, 
    newPlan: 'trial' | 'standard' | 'pro',
    oldPlan?: 'trial' | 'standard' | 'pro'
  ): Promise<void> {
    try {
      // 获取用户当前信息
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      // 如果没有提供旧计划，尝试获取当前计划
      if (!oldPlan) {
        oldPlan = await this.getCurrentUserPlan(userId);
      }

      // 如果计划没有变化，不需要调整
      if (oldPlan === newPlan) {
        console.log(`用户 ${userId} 订阅计划未变化，无需调整时间`);
        return;
      }

      // 获取计划配置
      const planDetails = await getPlanDetails();
      const oldLimit = planDetails[oldPlan].dailyTimeLimit;
      const newLimit = planDetails[newPlan].dailyTimeLimit;

      console.log(`用户 ${userId} 订阅变更: ${oldPlan}(${oldLimit}分钟) → ${newPlan}(${newLimit}分钟)`);

      // 计算时间差
      const timeDifference = newLimit - oldLimit;
      
      if (timeDifference > 0) {
        // 升级：增加时间
        await this.addDailyTime(userId, timeDifference * 60); // 转换为秒
        console.log(`✅ 升级奖励：为用户 ${userId} 增加 ${timeDifference} 分钟`);
      } else if (timeDifference < 0) {
        // 降级：保护用户，不减少当天剩余时间
        console.log(`✅ 降级保护：用户 ${userId} 当天剩余时间保持不变`);
        // 不做任何操作，保护用户利益
      }

    } catch (error) {
      console.error('订阅时间调整失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户当前的订阅计划
   */
  private static async getCurrentUserPlan(userId: string): Promise<'trial' | 'standard' | 'pro'> {
    const user = await User.findByPk(userId, {
      include: [{ model: Subscription, as: 'subscription' }]
    });

    if (!user) {
      throw new Error('用户不存在');
    }

    // 检查是否有有效订阅
    if (user.subscription) {
      const subscription = user.subscription as any;
      if (subscription.endDate > new Date() && subscription.status === 'active') {
        return subscription.plan;
      }
    }

    // 检查试用期
    if (user.trialEndDate && user.trialEndDate > new Date()) {
      return 'trial';
    }

    // 默认返回试用版
    return 'trial';
  }

  /**
   * 为用户增加每日剩余时间
   */
  private static async addDailyTime(userId: string, additionalSeconds: number): Promise<void> {
    const user = await User.findByPk(userId);
    if (!user) {
      throw new Error('用户不存在');
    }

    const currentRemaining = user.dailyRemainingSeconds || 0;
    const newRemaining = currentRemaining + additionalSeconds;

    await User.update(
      { 
        dailyRemainingSeconds: newRemaining,
        updatedAt: new Date()
      },
      { where: { id: userId } }
    );

    console.log(`用户 ${userId} 剩余时间: ${currentRemaining}秒 → ${newRemaining}秒 (+${additionalSeconds}秒)`);
  }

  /**
   * 重置用户每日时间为新计划的完整时间
   * 注意：这个方法会覆盖当前剩余时间，谨慎使用
   */
  static async resetDailyTimeToNewPlan(userId: string, newPlan: 'trial' | 'standard' | 'pro'): Promise<void> {
    try {
      const planDetails = await getPlanDetails();
      const newLimitSeconds = planDetails[newPlan].dailyTimeLimit * 60;

      await User.update(
        { 
          dailyRemainingSeconds: newLimitSeconds,
          lastResetDate: new Date(),
          updatedAt: new Date()
        },
        { where: { id: userId } }
      );

      console.log(`✅ 用户 ${userId} 每日时间重置为 ${newPlan} 计划的完整时间: ${newLimitSeconds}秒`);
    } catch (error) {
      console.error('重置每日时间失败:', error);
      throw error;
    }
  }

  /**
   * 获取订阅变更的时间调整预览
   * 用于在用户确认购买前显示时间变化
   */
  static async getTimeAdjustmentPreview(
    userId: string, 
    targetPlan: 'trial' | 'standard' | 'pro'
  ): Promise<{
    currentPlan: string;
    currentRemaining: number;
    targetPlan: string;
    newRemaining: number;
    adjustment: number;
    adjustmentType: 'upgrade' | 'downgrade' | 'same';
  }> {
    const user = await User.findByPk(userId);
    if (!user) {
      throw new Error('用户不存在');
    }

    const currentPlan = await this.getCurrentUserPlan(userId);
    const planDetails = await getPlanDetails();
    
    const currentLimit = planDetails[currentPlan].dailyTimeLimit;
    const targetLimit = planDetails[targetPlan].dailyTimeLimit;
    const timeDifference = targetLimit - currentLimit;
    
    const currentRemaining = user.dailyRemainingSeconds || 0;
    let newRemaining = currentRemaining;
    let adjustmentType: 'upgrade' | 'downgrade' | 'same' = 'same';

    if (timeDifference > 0) {
      // 升级
      newRemaining = currentRemaining + (timeDifference * 60);
      adjustmentType = 'upgrade';
    } else if (timeDifference < 0) {
      // 降级，保持当前剩余时间不变
      adjustmentType = 'downgrade';
    }

    return {
      currentPlan,
      currentRemaining,
      targetPlan,
      newRemaining,
      adjustment: timeDifference * 60, // 转换为秒
      adjustmentType
    };
  }
}

export default SubscriptionTimeAdjustmentService;
