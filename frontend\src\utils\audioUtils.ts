/**
 * 音频播放工具函数
 * 支持本地开发和云OSS部署环境
 */

// 当前播放的音频实例
let currentAudio: HTMLAudioElement | null = null;

// 获取OSS音频基础URL
const getOssAudioBaseUrl = (): string => {
  return import.meta.env.VITE_OSS_AUDIO_BASE_URL || '';
};

/**
 * 停止当前播放的音频
 */
export const stopCurrentAudio = (): void => {
  if (currentAudio) {
    currentAudio.pause();
    currentAudio.currentTime = 0;
    currentAudio = null;
  }
};

/**
 * 播放音频文件
 * @param audioUrl 音频文件URL或文件名
 * @param onEnd 播放结束回调
 * @param onError 播放失败回调
 * @returns Promise<boolean> 播放是否成功
 */
export const playAudio = async (
  audioUrl: string, 
  onEnd?: () => void, 
  onError?: (error: Error) => void
): Promise<boolean> => {
  try {
    // 停止当前播放的音频
    stopCurrentAudio();
    
    // 构建完整的音频URL
    let fullAudioUrl: string;
    
    // 如果是完整URL（包含http或https），直接使用
    if (audioUrl.startsWith('http://') || audioUrl.startsWith('https://')) {
      fullAudioUrl = audioUrl;
    } else {
      // 根据环境配置构建URL
      const ossBaseUrl = getOssAudioBaseUrl();
      if (ossBaseUrl) {
        // 生产环境：使用OSS基础URL
        fullAudioUrl = `${ossBaseUrl.replace(/\/$/, '')}/${audioUrl}`;
      } else {
        // 本地开发环境：从public/audio目录加载
        fullAudioUrl = `/audio/${audioUrl}`;
      }
    }
    
    console.log(`尝试播放音频: ${fullAudioUrl}`);
    
    // 创建新的音频实例
    currentAudio = new Audio(fullAudioUrl);
    
    // 设置音频属性
    currentAudio.volume = 0.8; // 设置音量为80%
    currentAudio.preload = 'auto';
    
    // 设置事件监听器
    const handleEnded = () => {
      console.log('音频播放结束');
      currentAudio = null;
      if (onEnd) onEnd();
    };
    
    const handleError = () => {
      const error = new Error(`音频加载或播放失败: ${fullAudioUrl}`);
      console.error('音频播放错误:', error);
      currentAudio = null;
      if (onError) onError(error);
    };
    
    currentAudio.addEventListener('ended', handleEnded);
    currentAudio.addEventListener('error', handleError);
    
    // 播放音频
    await currentAudio.play();
    console.log('音频开始播放');
    
    return true;
  } catch (error) {
    const playError = error instanceof Error ? error : new Error(`音频播放异常: ${String(error)}`);
    console.error('音频播放失败:', playError);
    currentAudio = null;
    if (onError) onError(playError);
    return false;
  }
};

/**
 * 检查音频文件是否可用
 * @param audioUrl 音频文件URL
 * @returns Promise<boolean>
 */
export const checkAudioAvailability = async (audioUrl: string): Promise<boolean> => {
  return new Promise((resolve) => {
    try {
      const audio = new Audio(audioUrl);
      
      const cleanup = () => {
        audio.removeEventListener('canplaythrough', onSuccess);
        audio.removeEventListener('error', onError);
      };

      const onSuccess = () => {
        cleanup();
        resolve(true);
      };

      const onError = () => {
        cleanup();
        resolve(false);
      };

      audio.addEventListener('canplaythrough', onSuccess);
      audio.addEventListener('error', onError);
      
      // 设置超时
      setTimeout(() => {
        cleanup();
        resolve(false);
      }, 5000);

      audio.load();
    } catch {
      resolve(false);
    }
  });
};

/**
 * 预加载音频文件
 * @param audioUrls 音频文件URL数组
 * @returns Promise<string[]> 成功预加载的URL数组
 */
export const preloadAudios = async (audioUrls: string[]): Promise<string[]> => {
  const results = await Promise.allSettled(
    audioUrls.map(url => checkAudioAvailability(url))
  );
  
  return audioUrls.filter((_, index) => {
    const result = results[index];
    return result.status === 'fulfilled' && result.value;
  });
};