import { Request, Response, NextFunction } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import configManagerService from '../services/configManager.service';
import uploadService from '../services/upload.service';

// 生成标准化的音频文件名
const generateStandardAudioFilename = (
  subscriptionPlan: string,
  studyState: string,
  distractedSubtype: string | undefined,
  messageIndex: number,
  fileExtension: string
): string => {
  const plan = subscriptionPlan.toLowerCase();
  let state = studyState.toLowerCase();
  
  // 处理分心状态的子类型
  if (studyState === 'DISTRACTED' && distractedSubtype) {
    state = distractedSubtype.toLowerCase();
  } else if (studyState === 'OFF_SEAT') {
    state = 'off_seat';
  }
  
  return `${plan}_${state}_${messageIndex + 1}.${fileExtension}`;
};

// 从反馈消息信息生成音频文件名
const generateAudioFilenameFromFeedback = (
  feedbackMessage: any,
  messageIndex: number,
  originalFilename: string
): string => {
  const fileExtension = path.extname(originalFilename).slice(1) || 'mp3';
  return generateStandardAudioFilename(
    feedbackMessage.subscriptionPlan,
    feedbackMessage.studyState,
    feedbackMessage.distractedSubtype,
    messageIndex,
    fileExtension
  );
};

// 扩展Request接口以支持文件上传
interface MulterRequest extends Request {
  file?: Express.Multer.File;
  files?: Express.Multer.File[];
}

// 获取本地上传中间件（仅在开发模式下使用）
const getLocalUploadMiddleware = () => {
  return uploadService.getLocalUploadMiddleware({
    destination: 'audio',
    fileFilter: uploadService.getAudioFileFilter(),
    limits: {
      fileSize: 10 * 1024 * 1024 // 10MB限制
    },
    filenamePrefix: 'audio'
  });
};

/**
 * 获取音频上传签名（OSS模式）
 * GET /api/audio/signature
 */
export const getAudioUploadSignature = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { filename } = req.query;
    
    if (!filename || typeof filename !== 'string') {
      return res.status(400).json({ message: '文件名参数是必需的' });
    }

    const signature = await uploadService.getUploadSignature(filename);
    res.json(signature);
  } catch (error) {
    if (error instanceof Error && error.message.includes('only available in production mode')) {
      return res.status(400).json({ message: 'OSS上传签名仅在生产环境下可用' });
    }
    next(error);
  }
};

/**
 * 保存音频文件URL（OSS模式）
 * POST /api/audio/save-url
 */
export const saveAudioUrl = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { audioUrl, originalName, size } = req.body;
    
    if (!audioUrl) {
      return res.status(400).json({ message: '音频URL是必需的' });
    }

    const filename = path.basename(audioUrl);
    
    res.json({
      message: '音频文件URL保存成功',
      audioUrl,
      filename,
      originalName: originalName || filename,
      size: size || 0
    });
  } catch (error) {
    next(error);
  }
};

/**
 * 上传语音文件（本地模式）
 */
export const uploadAudio = [
  (req: Request, res: Response, next: NextFunction) => {
    const config = uploadService.getUploadConfig();
    if (config.strategy === 'oss') {
      return res.status(400).json({ 
        message: '当前为OSS模式，请使用OSS上传流程',
        strategy: 'oss'
      });
    }
    
    const upload = getLocalUploadMiddleware();
    upload.single('audio')(req, res, next);
  },
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const multerReq = req as MulterRequest;
      if (!multerReq.file) {
        return res.status(400).json({ message: '请选择要上传的语音文件' });
      }

      const audioUrl = uploadService.generateFileUrl(multerReq.file.filename, 'audio');
      
      res.json({
        message: '语音文件上传成功',
        audioUrl,
        filename: multerReq.file.filename,
        originalName: multerReq.file.originalname,
        size: multerReq.file.size
      });
    } catch (error) {
      next(error);
    }
  }
];

/**
 * 删除语音文件
 */
export const deleteAudio = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(__dirname, '../../uploads/audio', filename);
    
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      res.json({ message: '语音文件删除成功' });
    } else {
      res.status(404).json({ message: '文件不存在' });
    }
  } catch (error: any) {
    console.error('删除语音文件失败:', error);
    res.status(500).json({ message: '删除失败', error: error.message });
  }
};

/**
 * 批量保存音频文件URL（OSS模式）
 * POST /api/audio/save-multiple-urls
 */
export const saveMultipleAudioUrls = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { audioFiles } = req.body;
    
    if (!audioFiles || !Array.isArray(audioFiles) || audioFiles.length === 0) {
      return res.status(400).json({ message: '音频文件列表是必需的' });
    }

    const savedFiles = audioFiles.map(file => ({
      filename: path.basename(file.audioUrl),
      originalName: file.originalName || path.basename(file.audioUrl),
      size: file.size || 0,
      audioUrl: file.audioUrl
    }));

    res.json({
      message: `成功保存${savedFiles.length}个音频文件URL`,
      files: savedFiles
    });
  } catch (error) {
    next(error);
  }
};

/**
 * 批量上传语音文件（本地模式）
 */
export const uploadMultipleAudio = [
  (req: Request, res: Response, next: NextFunction) => {
    const config = uploadService.getUploadConfig();
    if (config.strategy === 'oss') {
      return res.status(400).json({ 
        message: '当前为OSS模式，请使用OSS上传流程',
        strategy: 'oss'
      });
    }
    
    const upload = getLocalUploadMiddleware();
    upload.array('audios', 10)(req, res, next); // 最多10个文件
  },
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const multerReq = req as MulterRequest;
      const files = multerReq.files as any[];
      
      if (!files || files.length === 0) {
        return res.status(400).json({ message: '请选择要上传的语音文件' });
      }

      const uploadedFiles = files.map(file => ({
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
        audioUrl: uploadService.generateFileUrl(file.filename, 'audio')
      }));

      res.json({
        message: `成功上传${uploadedFiles.length}个语音文件`,
        files: uploadedFiles
      });
    } catch (error) {
      next(error);
    }
  }
];

/**
 * 上传反馈消息的语音文件（带自动重命名）
 */
export const uploadFeedbackAudio = [
  (req: Request, res: Response, next: NextFunction) => {
    const config = uploadService.getUploadConfig();
    if (config.strategy === 'oss') {
      return res.status(400).json({ 
        message: '当前为OSS模式，请使用OSS上传流程',
        strategy: 'oss'
      });
    }
    
    const upload = getLocalUploadMiddleware();
    upload.single('audio')(req, res, next);
  },
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const multerReq = req as MulterRequest;
      const { feedbackId, messageIndex } = req.body;
      
      if (!multerReq.file) {
        return res.status(400).json({ message: '请选择要上传的语音文件' });
      }
      
      if (!feedbackId || messageIndex === undefined) {
        return res.status(400).json({ message: '反馈消息ID和消息索引是必需的' });
      }

      // 获取反馈消息
      const feedbackMessage = await configManagerService.getFeedbackMessageById(feedbackId);
      if (!feedbackMessage) {
        return res.status(404).json({ message: '反馈消息不存在' });
      }
      
      const messageIdx = parseInt(messageIndex);
      if (messageIdx < 0 || messageIdx >= feedbackMessage.messages.length) {
        return res.status(400).json({ message: '消息索引无效' });
      }

      // 生成标准化文件名
      const standardFilename = generateAudioFilenameFromFeedback(
        feedbackMessage,
        messageIdx,
        multerReq.file.originalname
      );
      
      // 获取上传目录
      const uploadDir = uploadService.getUploadPath('audio');
      const oldPath = multerReq.file.path;
      const newPath = path.join(uploadDir, standardFilename);
      
      // 重命名文件
      if (fs.existsSync(newPath)) {
        fs.unlinkSync(newPath); // 删除已存在的文件
      }
      fs.renameSync(oldPath, newPath);
      
      const audioUrl = uploadService.generateFileUrl(standardFilename, 'audio');
      
      // 更新反馈消息的音频URL
      let audioUrls = feedbackMessage.audioUrls || [];
      while (audioUrls.length < feedbackMessage.messages.length) {
        audioUrls.push('');
      }
      audioUrls[messageIdx] = audioUrl;
      
      await feedbackMessage.update({ audioUrls });
      
      res.json({
        message: '语音文件上传并重命名成功',
        audioUrl,
        filename: standardFilename,
        originalName: multerReq.file.originalname,
        size: multerReq.file.size,
        feedbackMessage
      });
    } catch (error: any) {
      console.error('上传反馈消息语音失败:', error);
      res.status(500).json({ message: '上传失败', error: error.message });
    }
  }
];

/**
 * 更新反馈消息的语音文件
 */
export const updateFeedbackAudio = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { feedbackId, messageIndex, audioUrl } = req.body;
    
    // 获取反馈消息
    const feedbackMessage = await configManagerService.getFeedbackMessageById(feedbackId);
    if (!feedbackMessage) {
      return res.status(404).json({ message: '反馈消息不存在' });
    }

    let audioUrls = feedbackMessage.audioUrls || [];

    // 扩展数组长度以匹配messages
    while (audioUrls.length < feedbackMessage.messages.length) {
      audioUrls.push('');
    }

    // 更新指定索引的语音URL
    if (messageIndex >= 0 && messageIndex < feedbackMessage.messages.length) {
      audioUrls[messageIndex] = audioUrl || '';
      
      await feedbackMessage.update({ audioUrls });
      
      res.json({
        message: '语音文件更新成功',
        feedbackMessage
      });
    } else {
      res.status(400).json({ message: '消息索引无效' });
    }
  } catch (error: any) {
    console.error('更新反馈消息语音失败:', error);
    res.status(500).json({ message: '更新失败', error: error.message });
  }
};

/**
 * 批量管理反馈消息（启用/禁用/删除）
 */
export const batchManageFeedbackMessages = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { feedbackIds, action } = req.body;
    
    if (!feedbackIds || !Array.isArray(feedbackIds) || feedbackIds.length === 0) {
      return res.status(400).json({ message: '请提供要操作的反馈消息ID列表' });
    }
    
    if (!['enable', 'disable', 'delete'].includes(action)) {
      return res.status(400).json({ message: '操作类型无效，支持: enable, disable, delete' });
    }

    const results = [];
    
    for (const feedbackId of feedbackIds) {
      try {
        const feedbackMessage = await configManagerService.getFeedbackMessageById(feedbackId);
        if (!feedbackMessage) {
          results.push({ feedbackId, success: false, message: '反馈消息不存在' });
          continue;
        }

        if (action === 'delete') {
          // 删除关联的音频文件
          if (feedbackMessage.audioUrls) {
            for (const audioUrl of feedbackMessage.audioUrls) {
              if (audioUrl) {
                try {
                  const filename = path.basename(audioUrl);
                  const filePath = uploadService.getUploadPath('audio', filename);
                  if (fs.existsSync(filePath)) {
                    fs.unlinkSync(filePath);
                  }
                } catch (err) {
                  console.warn('删除音频文件失败:', err);
                }
              }
            }
          }
          
          await feedbackMessage.destroy();
          results.push({ feedbackId, success: true, message: '删除成功' });
        } else {
          const isActive = action === 'enable';
          await feedbackMessage.update({ isActive });
          results.push({ feedbackId, success: true, message: `${isActive ? '启用' : '禁用'}成功` });
        }
      } catch (error: any) {
        results.push({ feedbackId, success: false, message: error.message });
      }
    }
    
    res.json({
      message: '批量操作完成',
      results,
      successCount: results.filter(r => r.success).length,
      totalCount: results.length
    });
  } catch (error: any) {
    console.error('批量管理反馈消息失败:', error);
    res.status(500).json({ message: '批量操作失败', error: error.message });
  }
};