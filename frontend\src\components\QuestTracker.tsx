import React, { useState, useEffect } from 'react';
import api from '../services/api';

interface QuestTrackerProps {
    stage: 'STONE_MONKEY' | 'CAVE_MASTER' | 'MONKEY_KING' | 'TOTAL_MONKEY_KING';
    totalSpiritualPower: number; // 改为灵力值
    totalFocusSeconds?: number; // 保留用于兼容性，可选
}

interface GameConfig {
    stoneMonkeyGoalTokens: number;
    caveMasterGoalTokens: number;
    monkeyKingGoalTokens: number;
    totalMonkeyKingGoalTokens: number;
}

// 默认配置（用作fallback）
const DEFAULT_GAME_CONFIG: GameConfig = {
    stoneMonkeyGoalTokens: 1000,
    caveMasterGoalTokens: 3000,
    monkeyKingGoalTokens: 6000,
    totalMonkeyKingGoalTokens: 12000,
};

const getQuestDetails = (gameConfig: GameConfig) => ({
    STONE_MONKEY: {
        title: '石猴出世：称霸花果山',
        objective: `目标：累计修行灵力 ${gameConfig.stoneMonkeyGoalTokens.toLocaleString()} 点，进入水帘洞，即可成为水帘洞主！`,
        goalTokens: gameConfig.stoneMonkeyGoalTokens
    },
    CAVE_MASTER: {
        title: '水帘洞主：拜师学艺',
        objective: `目标：累计修行灵力 ${gameConfig.caveMasterGoalTokens.toLocaleString()} 点，拜师学艺，即可成为孙悟空！`,
        goalTokens: gameConfig.caveMasterGoalTokens
    },
    MONKEY_KING: {
        title: '孙悟空：龙宫取宝',
        objective: `目标：累计修行灵力 ${gameConfig.monkeyKingGoalTokens.toLocaleString()} 点，龙宫取宝，即可成为斗战胜佛！`,
        goalTokens: gameConfig.monkeyKingGoalTokens
    },
    TOTAL_MONKEY_KING: {
        title: '斗战胜佛：修成正果',
        objective: '恭喜！你已修成正果，成为斗战胜佛。继续勤勉修行，引领他人！',
        goalTokens: gameConfig.totalMonkeyKingGoalTokens
    },
});

const formatProgressSpiritualPower = (tokens: number, goalTokens: number) => {
    return `${tokens.toLocaleString()} / ${goalTokens.toLocaleString()} 灵力`;
};

const QuestTracker: React.FC<QuestTrackerProps> = ({ stage, totalSpiritualPower }) => {
    const [gameConfig, setGameConfig] = useState<GameConfig>(DEFAULT_GAME_CONFIG);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchGameConfig = async () => {
            try {
                const config = await api.config.getGameConfig();
                setGameConfig(config);
            } catch (error) {
                console.error('Failed to fetch game config:', error);
                // 使用默认配置
            } finally {
                setLoading(false);
            }
        };

        fetchGameConfig();
    }, []);

    if (loading) {
        return (
            <div className="my-6 p-4 bg-yellow-50 dark:bg-yellow-900/40 border-l-4 border-yellow-400 dark:border-yellow-500 rounded-r-lg">
                <div className="text-yellow-800 dark:text-yellow-200">加载中...</div>
            </div>
        );
    }

    const questDetails = getQuestDetails(gameConfig);
    const details = questDetails[stage] || questDetails.STONE_MONKEY;
    const progressPercentage = Math.min(100, (totalSpiritualPower / details.goalTokens) * 100);

    return (
        <div className="my-6 p-4 bg-yellow-50 dark:bg-yellow-900/40 border-l-4 border-yellow-400 dark:border-yellow-500 rounded-r-lg">
            <h3 className="font-bold text-lg text-yellow-800 dark:text-yellow-200">{details.title}</h3>
            <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">{details.objective}</p>
            
            {stage !== 'TOTAL_MONKEY_KING' && (
                <div className="mt-3">
                    <div className="flex justify-between items-center text-xs font-medium text-slate-600 dark:text-slate-300 mb-1">
                        <span>修行进度</span>
                        <span>{formatProgressSpiritualPower(totalSpiritualPower, details.goalTokens)}</span>
                    </div>
                    <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2.5">
                        <div 
                            className="bg-gradient-to-r from-orange-400 to-yellow-400 h-2.5 rounded-full transition-all duration-500"
                            style={{ width: `${progressPercentage}%` }}
                        ></div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default QuestTracker;