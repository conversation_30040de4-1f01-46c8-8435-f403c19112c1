# 系统管理后台使用说明

## 访问地址
管理后台独立入口：`http://localhost:3000/admin`

## 登录认证
- 默认管理员密钥：`admin123456`
- 可通过环境变量 `ADMIN_KEY` 自定义管理员密钥

## 功能模块

### 1. 数据看板
显示系统核心数据统计：
- **今日活跃用户**：当天有学习活动的用户数量
- **总用户数**：系统注册用户总数
- **今日新用户**：当天新注册的用户数量
- **当前在线用户**：实时在线用户数量
- **今日学习会话**：当天创建的学习会话数量
- **今日Token消耗**：当天AI分析消耗的Token总数
- **今日分析次数**：当天进行AI分析的总次数

### 2. Token统计
用户Token使用情况管理：
- **总计统计**：显示系统整体Token消耗情况
  - 总Token消耗
  - 输入Token
  - 输出Token  
  - 分析次数
- **用户列表**：分页显示所有用户的Token使用详情
  - 用户手机号
  - 注册时间
  - 今日Token消耗
  - 今日分析次数
  - 总Token消耗
  - 总分析次数
- **分页功能**：支持翻页查看用户数据

### 3. 用户反馈
用户反馈意见管理：
- **反馈列表**：显示用户提交的反馈内容
  - 用户手机号
  - 反馈内容
  - 提交时间
- **删除功能**：可删除不需要的反馈记录
- **分页功能**：支持翻页查看反馈数据

## API接口

### 认证方式
所有管理API请求需要在请求头中包含：
```
X-Admin-Key: admin123456
```

### 接口列表

#### 1. 数据看板统计
```
GET /api/admin/dashboard/stats
```

#### 2. 用户Token统计
```
GET /api/admin/users/token-stats?page=1&limit=20&sortBy=totalTokens&sortOrder=desc
```

#### 3. 用户反馈列表
```
GET /api/admin/feedback?page=1&limit=20
```

#### 4. 删除用户反馈
```
DELETE /api/admin/feedback/:id
```

## 安全说明

1. **管理员密钥**：建议在生产环境中设置复杂的管理员密钥
2. **访问控制**：管理后台应仅限内部人员访问
3. **HTTPS**：生产环境建议使用HTTPS协议
4. **日志记录**：建议记录管理操作日志

## 环境变量配置

在 `.env` 文件中添加：
```
ADMIN_KEY=your_secure_admin_key_here
```

## 注意事项

1. 管理后台与用户端系统完全独立，不会影响用户正常使用
2. 数据统计实时更新，反映系统当前状态
3. Token统计包含输入和输出Token，便于成本分析
4. 用户反馈可帮助改进系统功能和用户体验