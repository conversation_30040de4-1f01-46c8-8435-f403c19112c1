《悟空伴读 - 前端技术需求规格书》
1. 全局设计系统 (Global Design System)
1.1. 核心色板 (Color Palette)
The project primarily utilizes the default Tailwind CSS color palette. Specific color values are not explicitly defined in tailwind.config.js, indicating reliance on standard Tailwind classes. Key colors observed in use are:

背景色 (Background): bg-slate-900 (主背景), bg-slate-800/60 (卡片背景)
文字色 (Text): text-slate-100 (主要文字), text-white/70 (次要文字)
主色/高亮色 (Primary/Accent): A gradient from from-amber-300 to to-yellow-400 is used for the main title, suggesting a gold/yellow primary theme.
角色/插画颜色 (Character/Illustration Colors):
Stone Monkey Head: bg-[#d4a276]
Stone Monkey Face: bg-[#f6dcb0]
Stone Monkey Ears: bg-[#b98c5d]
1.2. 字体排印 (Typography)
主要字体 (Font Family): The primary font for the application is Inter, as defined in tailwind.config.js.
文本样式 (Text Styles):
H1 标题: text-4xl font-bold (e.g., "悟空伴读" title)
页脚文本: text-xs
1.3. 布局与间距 (Layout & Spacing)
基础间距单位 (Base Spacing Unit): The project uses the standard Tailwind CSS spacing scale, which is based on rem units and defaults to a 4px base increment (e.g., p-8 corresponds to 2rem or 32px).
常用布局模式 (Common Layout Patterns): The main layout is centered using Flexbox (flex items-center justify-center). The overall structure is a full-screen, single-page application view.
1.4. 核心图标 (Iconography)
The project does not appear to use a standard icon library like Heroicons or FontAwesome. Instead, it features custom-built SVG-like illustrations created with standard div elements and Tailwind CSS for styling, as seen in the StoneMonkeyCharacter component.

2. 全局状态管理 (Global State Management)
The application uses React Context for global state management, located in src/contexts/.

2.1. AuthContext (AuthContext.tsx)
用途 (Purpose): Manages all aspects of user authentication and session status. It provides a global state to determine if a user is logged in, their subscription details, and handles the logic for login, logout, and registration.
管理的状态 (State):
user: object | null - Stores user information (e.g., { phone: string }).
isTrialActive: boolean - Indicates if the user's trial period is active.
hasActiveSubscription: boolean - Indicates if the user has an active subscription.
planName: string - The name of the user's current subscription plan.
effectiveDailyLimit: number - The user's daily usage limit.
isParentVerified: boolean - Indicates if the parent has been verified.
subscriptionEndDate: string | undefined - The end date of the subscription.
trialEndDate: string | undefined - The end date of the trial.
isAuthenticated: boolean - A derived state indicating if the user is currently authenticated.
isLoading: boolean - Indicates if an authentication-related process (like initial status check) is in progress.
提供的函数 (Actions):
login(phone, password): Asynchronously logs the user in, stores the token, and refreshes the auth status.
register(phone, password): Asynchronously registers a new user.
logout(): Clears authentication data from state and localStorage.
refreshAuthStatus(): Asynchronously checks the validity of the stored token and updates the entire authentication state from the backend.
3. 路由结构 (Routing Structure)
The application uses react-router-dom for routing, configured within src/App.tsx.

公共路由 (Public Routes):
path="/*": This is a catch-all route. It conditionally renders either the DashboardView or the AuthView. If the user is authenticated (isAuthenticated is true), it shows DashboardView; otherwise, it directs to AuthView. AuthView itself handles the display of login, register, and forgot password forms, making them implicitly public.
私有路由 (Private Routes):
path="/admin": Renders the AdminDashboard component. Access is implicitly private as it's likely only reachable by users with specific roles, though the routing logic itself doesn't show a role-based guard.
path="/admin/config": Renders the AdminConfigView component.
path="/admin/advanced-config": Renders the AdminAdvancedConfigView component.
path="/*" (when authenticated): Renders the DashboardView, which is the main application view for logged-in users.
4. 页面级详细规格 (Page-Level Specifications)
4.1. 认证页面 (AuthView.tsx)
4.1.1. 页面描述: This view serves as the main entry point for unauthenticated users. It is not a single page but a container that orchestrates the display of the login, registration, and password recovery forms. Its goal is to provide a seamless authentication experience.
4.1.2. 页面布局 (Layout):
PlainText



+--------------------------------------------------+|                                                  ||            [悟空伴读 - Title]                      ||                                                  ||   +------------------------------------------+   ||   |                                          |   ||   |      <LoginView />                       |   ||   |         or                               |   ||   |      <RegisterView />                    |   ||   |         or                               |   ||   |      <ForgotPasswordView />              |   ||   |                                          |   ||   +------------------------------------------+   ||                                                  |+--------------------------------------------------+| [StoneMonkeyCharacter Animation]                 |+--------------------------------------------------+
4.1.3. 组件构成 (Component Composition):
LoginView: The user login form. (Path: ./LoginView.tsx)
RegisterView: The user registration form. (Path: ./RegisterView.tsx)
ForgotPasswordView: The password recovery form. (Path: ./ForgotPasswordView.tsx)
4.1.4. 状态与逻辑 (State & Logic):
useState:
isRegistering: boolean - Toggles between the login and registration views.
isForgotPassword: boolean - Toggles the display of the forgot password view.
全局 Context: This view does not directly call any Context functions but renders components (LoginView, RegisterView) that do.
API 服务: This view does not directly call any API services but renders components that do.
4.1.5. 用户交互流程 (User Interaction Flow):
当用户 访问应用但未登录，则系统 显示 AuthView，默认展示 LoginView 组件。
当用户 在 LoginView 中点击 "注册新账号" 链接，则系统 将 isRegistering 状态设置为 true，从而卸载 LoginView 并渲染 RegisterView。
当用户 在 RegisterView 中点击 "返回登录" 链接，则系统 将 isRegistering 状态设置为 false，从而卸载 RegisterView 并渲染 LoginView。
当用户 在 LoginView 中点击 "忘记密码" 链接，则系统 将 isForgotPassword 状态设置为 true，从而卸载 LoginView 并渲染 ForgotPasswordView。
当用户 在 ForgotPasswordView 中点击 "返回登录"，则系统 将 isForgotPassword 状态设置为 false，从而卸载 ForgotPasswordView 并渲染 LoginView。