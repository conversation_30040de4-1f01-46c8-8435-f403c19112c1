{"name": "ai-study-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "node_modules/.bin/vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.6", "@types/recharts": "^1.8.29", "axios": "^1.7.2", "howler": "^2.2.4", "react": "^18.2.0", "react-circular-progressbar": "^2.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-router-dom": "^6.23.1", "recharts": "^3.1.2"}, "devDependencies": {"@types/howler": "^2.2.12", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "tailwindcss": "3.3.0", "typescript": "^5.2.2", "vite": "^5.2.0"}}