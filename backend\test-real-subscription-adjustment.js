// 实际测试订阅时间调整功能
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'database.sqlite');
const targetPhone = '13671910820';

console.log('🧪 实际测试订阅时间调整功能...');

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    return;
  }
  console.log('✅ 成功连接到SQLite数据库');
});

// 获取用户当前状态
function getCurrentUserState() {
  return new Promise((resolve, reject) => {
    db.get(`
      SELECT 
        u.id,
        u.phone,
        u.dailyRemainingSeconds,
        u.trialEndDate,
        s.plan as subscription_plan,
        s.status as subscription_status,
        s.endDate as subscription_end
      FROM users u
      LEFT JOIN subscriptions s ON u.id = s.userId
      WHERE u.phone = ?
    `, [targetPhone], (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

// 模拟设置用户为试用版状态
function setUserToTrialState(userId, remainingMinutes) {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // 删除现有订阅
      db.run(`DELETE FROM subscriptions WHERE userId = ?`, [userId], (err) => {
        if (err) {
          reject(err);
          return;
        }
      });
      
      // 设置试用期
      const trialEndDate = new Date();
      trialEndDate.setDate(trialEndDate.getDate() + 7); // 7天试用期
      
      // 更新用户状态
      db.run(`
        UPDATE users 
        SET 
          dailyRemainingSeconds = ?,
          trialEndDate = ?,
          lastResetDate = datetime('now')
        WHERE id = ?
      `, [remainingMinutes * 60, trialEndDate.toISOString(), userId], function(err) {
        if (err) {
          reject(err);
        } else {
          console.log(`✅ 设置用户为试用版状态，剩余 ${remainingMinutes} 分钟`);
          resolve();
        }
      });
    });
  });
}

// 模拟创建标准版订阅
function createStandardSubscription(userId) {
  return new Promise((resolve, reject) => {
    const startDate = new Date();
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + 1);
    
    db.run(`
      INSERT INTO subscriptions (userId, plan, status, startDate, endDate, alipayTradeNo, createdAt, updatedAt)
      VALUES (?, 'standard', 'active', ?, ?, 'test-trade-no', datetime('now'), datetime('now'))
    `, [userId, startDate.toISOString(), endDate.toISOString()], function(err) {
      if (err) {
        reject(err);
      } else {
        console.log(`✅ 创建标准版订阅`);
        resolve();
      }
    });
  });
}

// 手动执行时间调整逻辑
function manualTimeAdjustment(userId, oldPlan, newPlan) {
  return new Promise((resolve, reject) => {
    // 获取计划配置
    const planLimits = {
      trial: 60,
      standard: 180,
      pro: 300
    };
    
    const oldLimit = planLimits[oldPlan];
    const newLimit = planLimits[newPlan];
    const timeDifference = newLimit - oldLimit;
    
    if (timeDifference > 0) {
      // 升级：增加时间
      db.get(`SELECT dailyRemainingSeconds FROM users WHERE id = ?`, [userId], (err, row) => {
        if (err) {
          reject(err);
          return;
        }
        
        const currentRemaining = row.dailyRemainingSeconds || 0;
        const newRemaining = currentRemaining + (timeDifference * 60);
        
        db.run(`
          UPDATE users 
          SET dailyRemainingSeconds = ?
          WHERE id = ?
        `, [newRemaining, userId], function(err) {
          if (err) {
            reject(err);
          } else {
            console.log(`✅ 升级调整：${currentRemaining}秒 → ${newRemaining}秒 (+${timeDifference * 60}秒)`);
            resolve({ adjustment: timeDifference * 60, newRemaining });
          }
        });
      });
    } else {
      // 降级或无变化：不调整
      console.log(`✅ 降级保护：时间保持不变`);
      resolve({ adjustment: 0, newRemaining: null });
    }
  });
}

async function runRealTest() {
  try {
    console.log('\n📊 测试开始 - 模拟试用版升级到标准版');
    
    // 1. 获取用户当前状态
    const initialState = await getCurrentUserState();
    console.log('\n1️⃣ 初始状态:');
    console.log('用户ID:', initialState.id);
    console.log('当前订阅:', initialState.subscription_plan || 'trial');
    console.log('剩余时间:', Math.floor(initialState.dailyRemainingSeconds / 60), '分钟');
    
    // 2. 设置为试用版状态（模拟用户使用了30分钟）
    console.log('\n2️⃣ 设置试用版状态（已使用30分钟，剩余30分钟）:');
    await setUserToTrialState(initialState.id, 30);
    
    const trialState = await getCurrentUserState();
    console.log('试用版剩余时间:', Math.floor(trialState.dailyRemainingSeconds / 60), '分钟');
    
    // 3. 模拟升级到标准版
    console.log('\n3️⃣ 升级到标准版:');
    await createStandardSubscription(initialState.id);
    
    // 4. 执行时间调整
    console.log('\n4️⃣ 执行时间调整:');
    const adjustmentResult = await manualTimeAdjustment(initialState.id, 'trial', 'standard');
    
    // 5. 验证最终结果
    console.log('\n5️⃣ 最终结果验证:');
    const finalState = await getCurrentUserState();
    console.log('最终订阅:', finalState.subscription_plan);
    console.log('最终剩余时间:', Math.floor(finalState.dailyRemainingSeconds / 60), '分钟');
    
    // 6. 验证逻辑
    console.log('\n6️⃣ 逻辑验证:');
    const expectedRemaining = 30 + 120; // 试用版剩余30分钟 + 升级奖励120分钟
    const actualRemaining = Math.floor(finalState.dailyRemainingSeconds / 60);
    
    console.log('期望剩余时间:', expectedRemaining, '分钟');
    console.log('实际剩余时间:', actualRemaining, '分钟');
    console.log('验证结果:', expectedRemaining === actualRemaining ? '✅ 通过' : '❌ 失败');
    
    if (expectedRemaining === actualRemaining) {
      console.log('\n🎉 测试成功！订阅升级时间调整功能正常工作。');
      console.log('用户从试用版升级到标准版，获得了120分钟的额外时间奖励。');
    } else {
      console.log('\n❌ 测试失败！时间调整逻辑有问题。');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('关闭数据库失败:', err.message);
      } else {
        console.log('\n🔌 数据库连接已关闭');
      }
    });
  }
}

runRealTest();
