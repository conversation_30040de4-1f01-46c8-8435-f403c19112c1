import { connectDB, sequelize } from '../config/db';
import { QueryInterface, DataTypes } from 'sequelize';

/**
 * 添加时间限制字段到AppConfig表的迁移脚本
 */
async function addTimeLimitFields() {
  try {
    console.log('🔄 开始添加时间限制字段到AppConfig表...');
    
    // 连接数据库
    await connectDB();
    
    const queryInterface: QueryInterface = sequelize.getQueryInterface();
    
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    const appConfigTableName = 'app_configs';
    
    if (!tables.includes(appConfigTableName)) {
      console.log(`❌ 表 ${appConfigTableName} 不存在`);
      return;
    }
    
    // 获取表的当前结构
    const tableDescription = await queryInterface.describeTable(appConfigTableName);
    console.log('📋 当前表结构:', Object.keys(tableDescription));
    
    // 要添加的字段列表
    const fieldsToAdd = [
      {
        name: 'trialDailyLimitMinutes',
        definition: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 60,
        }
      },
      {
        name: 'standardDailyLimitMinutes',
        definition: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 180,
        }
      },
      {
        name: 'proDailyLimitMinutes',
        definition: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 300,
        }
      }
    ];
    
    // 逐个添加字段
    for (const field of fieldsToAdd) {
      if (!tableDescription[field.name]) {
        console.log(`➕ 添加字段: ${field.name}`);
        try {
          await queryInterface.addColumn(appConfigTableName, field.name, field.definition);
          console.log(`✅ 成功添加字段: ${field.name}`);
        } catch (error: any) {
          if (error.message.includes('duplicate column name')) {
            console.log(`⚠️  字段 ${field.name} 已存在，跳过`);
          } else {
            console.error(`❌ 添加字段 ${field.name} 失败:`, error.message);
            throw error;
          }
        }
      } else {
        console.log(`⚠️  字段 ${field.name} 已存在，跳过`);
      }
    }
    
    // 验证字段是否添加成功
    const updatedTableDescription = await queryInterface.describeTable(appConfigTableName);
    console.log('📋 更新后表结构:', Object.keys(updatedTableDescription));
    
    // 检查是否有现有记录需要更新默认值
    const [results] = await sequelize.query(`SELECT COUNT(*) as count FROM ${appConfigTableName}`);
    const recordCount = (results as any)[0].count;
    
    if (recordCount > 0) {
      console.log(`📝 发现 ${recordCount} 条现有记录，更新默认值...`);
      
      // 更新现有记录的默认值
      await sequelize.query(`
        UPDATE ${appConfigTableName} 
        SET 
          trialDailyLimitMinutes = COALESCE(trialDailyLimitMinutes, 60),
          standardDailyLimitMinutes = COALESCE(standardDailyLimitMinutes, 180),
          proDailyLimitMinutes = COALESCE(proDailyLimitMinutes, 300)
        WHERE 
          trialDailyLimitMinutes IS NULL 
          OR standardDailyLimitMinutes IS NULL 
          OR proDailyLimitMinutes IS NULL
      `);
      
      console.log('✅ 现有记录默认值更新完成');
    }
    
    console.log('🎉 时间限制字段添加完成！');
    
  } catch (error) {
    console.error('❌ 迁移失败:', error);
    throw error;
  } finally {
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addTimeLimitFields()
    .then(() => {
      console.log('✅ 迁移脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 迁移脚本执行失败:', error);
      process.exit(1);
    });
}

export default addTimeLimitFields;
